import { heroui } from "@heroui/react";

/** @type {import("tailwindcss").Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/layouts/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        RedHatDisplay: ["RedHatDisplay", "sans-serif"],
      },
      colors: {
        purple1: "#8F7EFF",
        purple2: "#AB7EFF",
        purple3: "#6033C9",
        purple4: "#411C9C",
        border: "#00000033",
      },
    },
  },
  darkMode: "class",
  plugins: [
    heroui({
      prefix: "tube",
      layout: {},
      themes: {
        light: {
          colors: {
            color1: "#FFFFFF",
            color2: "#F8F8FA",
            color3: "#F8F8FA",
            color4: "#20202C",
            color5: "#474A4D",
            color6: "#FFFFFF",
            color7: "#33333380",
            color8: "#333333",
            color9: "#0000004D",
          },
        },
        dark: {
          colors: {
            color1: "#FFFFFF0D",
            color2: "#0B0A12",
            color3: "#C7CFD61A",
            color4: "#20202C",
            color5: "#C7CFD6",
            color6: "#121223",
            color7: "#FFFFFF99",
            color8: "#FFFFFF",
            color9: "#FFFFFF4D",
          },
        },
      },
    }),
  ],
};
