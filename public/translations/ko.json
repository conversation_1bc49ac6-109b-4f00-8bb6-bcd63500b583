{"hello": "안녕하세요 ${msg}", "wrong.network": "지원되지 않는 MetaMask 네트워크", "connect.wallet": "지갑 연결", "address.copied": "주소가 복사됩니다.", "invalid.input": "잘못된 입력", "submit": "제출하기", "input.insufficient.depositFee": "수수료로 ${fee} IOTX 필요", "input.wallet.not_connected": "지갑이 연결되지 않았습니다.", "input.token.unselected": "토큰을 선택해 주세요.", "input.cashier.invalid": "잘못된 계산원 계약 주소", "input.swap.invalid": "잘못된 스왑 계약 주소", "input.token.invalid": "잘못된 토큰 컨트랙트", "input.amount.invalid": "잘못된 금액", "input.amount.enter_value": "금액 입력", "input.amount.range_error": "금액 범위 오류", "input.balance.insufficient": "${symbol} 잔고가 부족합니다", "input.balance.invalid": "잘못된 ${symbol} 잔액", "input.depositfee.error": "입금 수수료 오류", "input.ioaddress.invalid": "잘못된 IoTeX 주소", "input.crossschainaddress.invalid": "잘못된 ${chain} 주소", "deposit": "입금", "approve": "승인", "fee": "요금", "fee.relay": "중계 수수료", "fee.tube": "튜브 요금", "fee.network": "네트워크 요금", "relay_to_chain": "${chain} 으로 중계", "confirm": "확인", "you_are_going_to_deposit": "입금하려고 합니다.", "you_are_going_to_withdraw": "탈퇴하려고 합니다.", "to_iotube_and_mint": "를 구독하고", "change_network_eth_warning": "MetaMask는 귀하를 위해 Ethereum으로 전환하는 것을 허용하지 않습니다. MetaMask 내부로 전환해주세요.", "deposit.approving": "승인", "button.waiting": "대기 중", "button.confirming": "확인", "complete.broadcast_transaction_successfully": "트랜잭션이 성공적으로 제출되었습니다.", "complete.tx_broadcast_network": "${network} 네트워크에 대한 ${confirmationTimes} 확인 및 2/3 증인으로부터 추가 확인 후, ${amount} ${token} ${chain} 의 귀하의 주소로 전송됩니다:", "complete.your_tx_chain": "귀하의 ${chain} TX 해시", "complete.check_status_comment": "지금 바로 다음에서 상태를 확인할 수 있습니다.", "eta": "ETA", "network_confirmations": "${network} 확인", "iotex_confirmations": "IoTeX 확인", "witness_confirmation": "증인 확인", "min": "분", "sec": "초", "may_delay_comment": "${network} 네트워크 정체로 인해 지연될 수 있습니다.", "address_copied": "주소 복사 완료!", "transaction_link_copied": "거래 링크가 복사되었습니다!", "info.features": "탈중앙화. 다중 자산. 크로스 체인 브리지.", "info.summary": "교환을 위한 안전하고 빠른 보안 브리지", "info.summary.next": "자산을 IoTeX와 블록체인 세계 간에 교환할 수 있습니다.", "info.how_iotex_work": "ioTube 작동 방식", "info.chain": "체인", "info.assets": "자산", "info.total_value_locked": "총 자산", "faq.what_is_iotube_bridge": "ioTube Bridge란 무엇인가요?", "faq.iotube_bridge_is": "ioTube는 탈중앙화 크로스체인 브리지로, IoTeX와 다른 블록체인 네트워크 간에 암호화 자산(예: 대체 가능/비대체 가능 토큰, 스테이블 코인)을 양방향으로 교환할 수 있게 해줍니다.", "faq.iotube_ui": "ioTube UI는 다음에서 액세스할 수 있습니다.", "faq.what_is_iotube_used_for": "ioTube는 어떤 용도로 사용되나요?", "faq.iotube_used_for.one": "ioTube는 새로운 자산, 데이터, 유동성을 IoTeX 네트워크에 가져오는 즉각적인 사용 사례 외에도, ioTube를 활용하여 필요한 암호화 자산과 데이터를 포팅함으로써 IoTeX 네트워크의 기능을 크게 확장할 수 있는 잠재력을 가지고 있습니다: ${chain} 및 기타 블록체인의 백엔드 프로세서가 될 수 있습니다.", "faq.iotube_used_for.two": "장기적으로는 IoT 디바이스 데이터와 같이 IoTeX 블록체인에서 생성된 고유 자산을 사용하여 ${chain} 및 다른 블록체인의 스마트 컨트랙트에서 로직을 트리거할 수 있습니다. 다시 말해, IoTeX의 블록체인은 비 IoTeX 컨트랙트의 오라클 역할을 하거나 그 반대의 역할을 할 수 있습니다.", "faq.how_does_iotube_work": "ioTube는 어떻게 작동하나요?", "faq.iotube_work.two_components": "ioTube는 두 가지 핵심 구성 요소로 구성됩니다.", "faq.iotube_work.components.smart_contracts": "스마트 컨트랙트 세트", "faq.iotube_work.components.smart_contracts.work": "IoTeX 및 기타 블록체인에 사전 배포됩니다.", "faq.iotube_work.components.pool_of_witness": "\"증인\" 풀", "faq.iotube_work.components.pool_of_witness.work": "를 생성하여 IoTeX 및 기타 블록체인의 스마트 컨트랙트에서 ioTube 작업을 수신합니다. 한 블록체인에서 토큰 잠금 작업이 감지되면 증인 풀이 이를 검증하고 확정된 정보를 다른 블록체인에 전달하며, 여기서 동일한 양의 '프록시 토큰'이 발행됩니다. 반대로, 한 블록체인에서 프록시 토큰 소각이 감지되면 증인 풀이 이를 검증하고 확정된 정보를 다른 블록체인에 전달하며, 여기서 동일한 양의 원본 토큰이 잠금 해제됩니다.", "faq.what_is_proxy_token": "프록시 토큰이란 무엇인가요?", "faq.proxy_token_is": "\"프록시 토큰\"은 다른 블록체인에 존재하는 다른 토큰의 표현입니다(예: IoTeX의 ioWETH 토큰은 이더리움의 WETH 토큰의 IoTeX \"프록시\" 버전이 될 수 있습니다).", "faq.what_different_proxy_token_and_original_token": "프록시 토큰과 원본 토큰의 차이점은 무엇인가요?", "faq.difference_between_proxy_token_and_original_token": "프록시 토큰은 원본 토큰의 정확한 복사본으로, 동일한 속성을 가지며 원본 토큰과 동일한 방식으로 사용할 수 있습니다. 다만 원본 토큰과 다른 블록체인에 존재할 뿐입니다.", "faq.does_the_token_supply_increase_when_using_iotube": "ioTube를 사용하면 토큰 공급량이 증가하나요?", "faq.token_supply_does_not_increase": "아니요: ioTube 브릿지는 한 블록체인에서 일정량의 토큰을 잠그고(본질적으로 유통되지 않게 함), 다른 블록체인에서 모든 면에서 원본 토큰을 나타내는(즉, 잠긴 공급량을 재생성하는) 정확히 동일한 양의 프록시 토큰을 채굴하기 때문에 원본 토큰의 공급량은 절대 변하지 않습니다. 결과적으로 원래 토큰의 유통량은 동일하게 유지되며, 단지 하나의 블록체인이 아닌 두 개의 다른 블록체인으로 분할됩니다.", "faq.what_happens_to_my_original_tokens_if_i_sell_the_proxy_token": "프록시 토큰을 판매하면 원래 토큰은 어떻게 되나요?", "faq.happens_to_original_tokens": "ioTube를 사용하여 이더리움에서 IoTeX로 원본 토큰을 전송하면, 원본 토큰은 ioTube 컨트랙트에 저장되고 잠기므로 더 이상 이더리움에서 해당 토큰을 소유하지 않게 됩니다. 반면에, 사용자는 이제 IoTeX 블록체인에 전송된 것과 동일한 양의 프록시 토큰을 소유하게 됩니다.", "faq.can_i_send_my_proxy_token_back": "프록시 토큰을 IoTeX에서 이더리움으로 다시 보낼 수 있나요?", "faq.can_send_proxy_token_back": "예: 언제든지 IoTeX에서 이더리움으로 프록시 토큰을 전송하고 이더리움에서 동일한 금액의 원본 토큰을 다시 받을 수 있습니다.", "faq.can_i_transfer_as_many_or_limit": "원하는 만큼 토큰을 전송할 수 있나요, 아니면 한도가 있나요?", "faq.transfer_limit": "ioTube를 안정적으로 실행하기 위해 이동할 수 있는 에셋의 최소/최대 양을 제한했습니다. 이러한 제한은 ioTube의 스트레스 검증이 완료되면 해제될 수 있습니다.", "faq.what_are_the_fees_using_iotube": "ioTube 사용료는 얼마인가요?", "faq.fee.service": "ioTube 서비스 수수료: 0", "faq.fee.service.desc": "ioTube 서비스 자체 이용에 대한 수수료는 없습니다.", "faq.fee.from_eth_to_iotex": "다른 블록체인에서 IoTeX로:", "faq.fee.from_eth_to_iotex.desc.one": "다른 블록체인에 토큰을 잠그면 일반 거래 수수료를 지불하게 됩니다.", "faq.fee.from_eth_to_iotex.desc.two": "IoTeX 블록체인에서 프록시 토큰 발행 거래에 대한 비용을 지불하지 않습니다.", "faq.fee.from_iotex_to_eth": "IoTeX에서 다른 블록체인까지:", "faq.fee.from_iotex_to_eth.desc.one": "IoTeX 블록체인에서 프록시 토큰 소각 거래에 대한 비용을 지불하지 않습니다.", "faq.fee.from_iotex_to_eth.desc.two": "다른 블록체인의 잠금 해제 거래 수수료를 충당하기 위해 소량의 IOTX를 지불하게 됩니다.", "faq.is_there_is_a_tutorial": "ioTube 사용 방법을 설명하는 튜토리얼이 있나요?", "faq.tutorial_community": "예: IoTeX 커뮤니티 포럼에서 이 ioTube 튜토리얼을 확인하세요:", "faq.is_iotube_open_source": "ioTube는 오픈 소스인가요?", "faq.iotube_github": "예: ioTube 코드는 오픈 소스이며 GitHub에서 찾을 수 있습니다:", "transaction.status.tips.unknown": "알려지지 않은", "transaction.status.tips.created": "유효성 검사", "transaction.status.tips.submitted": "보내기", "transaction.status.tips.settled": "정산", "transaction.status.tips.failed": "실패", "from": "보낸 사람", "to": "받는 분", "hash": "해시", "status": "상태", "asset": "자산", "amount": "금액", "age": "나이", "ago": "전", "day": "일", "days": "여러 날", "hour": "시", "hours": "시간", "just_now": "방금", "minute": "분", "minutes": "분", "month": "월", "months": "개월", "year": "년도", "years": "년", "enter_app": "앱 시작", "view_transactions": "ioTube 탐색기 >>", "tube_v4": "ioTube v5는 Web3 지갑(MetaMask, ioPay 모바일 등)에서만 작동합니다.", "go_to": "이동", "switch_network.header": "${network_name} 으로 전환하시겠습니까?", "switch_network.confirm.content": "지금 ${network_name} 으로 전환 중입니다. ${wallet} 또는 브라우저 기반 지갑에서 확인하시기 바랍니다.", "button.yes": "예", "button.no": "아니요", "account": "계정", "button.logout": "로그아웃", "view_iotexscan": "IoTeXScan에서 보기", "free": "무료", "button.switch": "스위치", "button.cancel": "취소", "ccToken.swap.confirm": "스왑 확인", "button.swap": "교환", "asset.title": "ioTube의 자산", "asset.supported": "토큰을 지원받고 싶으신가요?", "asset.start": "여기에서 시작 >>>", "ethAddress.tooltip": "이것은 귀하의 개인 키를 기반으로 한 Web3(0x) 지갑입니다. ${keyword}", "iotexAddress.tooltip": "이것은 개인 키를 기반으로 하는 IoTeX 지갑입니다. ${keyword}", "my.wallet": "내 지갑", "button.okay": "좋아요", "exchange.address.warning": "경고: 거래소 주소나 컨트랙트 주소를 사용하지 마세요. 토큰을 분실할 수 있습니다.", "swap.ctoken.title": "크로스체인 토큰", "swap.ctoken.description": "CIOTX와 같은 크로스체인 토큰은 아이오튜브를 통해 모든 네트워크에 연결할 수 있는 랩드 토큰입니다. 언제든지 1:1 비율로 토큰을 랩핑하거나 언랩핑할 수 있습니다. 다른 토큰을 보시려면 다른 네트워크로 전환해 주세요.", "swap.ctoken.not_support": "CToken은 IoTeX 및 이더리움 네트워크에서 사용할 수 있습니다. 계속하려면 IoTeX 또는 이더리움으로 전환하세요.", "home.button.add_iotex_net": "IoTeX 네트워크에 추가/전환", "home.button.use_third_bridge": "를 사용하거나 제삼자리:", "button.add_token_metamask": "${token} (를) MetaMask에 추가하세요", "token.quick_swap": "스왑 ${tokenA}/${tokenB}", "wallets.description": "지갑 주소를 입력하고 IoTeX에서 자산을 확인하세요. ioTube가 지원하는 모든 체인의 모든 자산에 대한 전체 목록을 보려면 다음을 방문하십시오. ", "metamask_connect_msg": "지갑(MetaMask)을 사용하여 보류 중인 권한을 승인하십시오.", "ledger.connection.description": "IoTeX 네트워크 전용", "faq.how_to_bridge_iotexe_title": "IOTX-E(ERC20 IoTeX 토큰)를 어떻게 브리지할 수 있나요?", "faq.how_to_bridge_iotexe_content": "IOTX-E는 Ethereum의 레거시 IoTeX ERC20 토큰입니다. 후에", "faq.how_to_bridge_iotexe_content1": ", CIOTX는 IoTeX와 Ethereum 사이에서 사용됩니다. IOTX-E 토큰을 IoTeX 네트워크에 연결하려면 다음을 사용하여 변환할 수 있습니다.", "faq.how_to_bridge_iotexe_content2": "또는 다른 DEX를 사용하여 먼저 CIOTX로 스왑합니다.", "withdraw.warning": "IOTX 브리지?", "withdraw.warning.link": "먼저 여기에 래핑해주세요 >>", "Wrap": "캡슐화", "Unwrap": "차단 해제", "Select.Token": "토큰 선택", "no.depin.tokens": "검색 결과가 없습니다.", "All.Tokens": "모든 토큰", "DePIN.Tokens": "DePIN 토큰", "withdraw": "철회", "complete.token_on": "${chain} 의 토큰:", "button.disconnect": "연결 해제", "deposit.polygon_gas_hight": "경고: 현재 Polygon의 가스 가격이 500gwei 이상으로 인상되었기 때문에 Polygon 네트워크로의 전송이 지연될 수 있습니다.", "first.use.iotube": "LoTeX의 초기 거래에 대한 가스 요금을 충당하기 위해 ioTube를 처음 사용할 때 0.5 lOTX 에어드랍을 받아야 합니다."}