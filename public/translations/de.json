{"hello": "hallo ${msg}", "wrong.network": "Nicht unterstütztes MetaMask-Netzwerk", "connect.wallet": "Wallet verbinden", "address.copied": "Die Adresse wird kopiert.", "invalid.input": "Ungültige Eingabe", "submit": "Einreichen", "input.insufficient.depositFee": "Benötigt ${fee} IOTX für Gebühr", "input.wallet.not_connected": "Wallet ist nicht verbunden", "input.token.unselected": "Bitte Token auswählen", "input.cashier.invalid": "Ungültige Adresse des Kassenvertrags", "input.swap.invalid": "Ungültige Swap-Vertragsadresse", "input.token.invalid": "Ungültiger Token-Vertrag", "input.amount.invalid": "Ungültiger Betrag", "input.amount.enter_value": "<PERSON><PERSON> e<PERSON>ben", "input.amount.range_error": "Fehler im Betragsbereich", "input.balance.insufficient": "Unzureichendes ${symbol} -Guthaben", "input.balance.invalid": "Ungültiger ${symbol} -Guthaben", "input.depositfee.error": "Fehler bei der Einzahlungsgebühr", "input.ioaddress.invalid": "Ungültige IoTeX-Adresse", "input.crossschainaddress.invalid": "Ungültige ${chain} -<PERSON><PERSON><PERSON>", "deposit": "Einzahlung", "approve": "<PERSON><PERSON><PERSON><PERSON>", "fee": "<PERSON><PERSON><PERSON><PERSON>", "fee.relay": "Relais-<PERSON><PERSON><PERSON><PERSON>", "fee.tube": "Gebühr für das Rohr", "fee.network": "Netzwerkgebühr", "relay_to_chain": "Weiterleitung an ${chain}", "confirm": "Bestätigen", "you_are_going_to_deposit": "Sie werden Folgendes einzahlen", "you_are_going_to_withdraw": "<PERSON>e werden sich zurückziehen", "to_iotube_and_mint": "von ${network} und erhalten", "change_network_eth_warning": "MetaMask erlaubt uns nicht, für Sie zu Ethereum zu wechseln. Bitte wechseln Sie innerhalb von MetaMask.", "deposit.approving": "<PERSON><PERSON><PERSON><PERSON>", "button.waiting": "<PERSON><PERSON>", "button.confirming": "Bestätigen Sie", "complete.broadcast_transaction_successfully": "Transaktion erfolgreich übermittelt", "complete.tx_broadcast_network": "Nach ${confirmationTimes} Bestätigung des ${network} -Netzwerks und weiteren Bestätigungen von 2/3 Zeugen wird ${amount} ${token} an Ihre Adresse auf ${chain} gesendet:", "complete.your_tx_chain": "Ihr ${chain} TX-Hash", "complete.check_status_comment": "<PERSON>e können den Status gerade jetzt überprüfen unter", "eta": "ETA", "network_confirmations": "${network} Bestätigungen", "iotex_confirmations": "IoTeX-Bestätigungen", "witness_confirmation": "Zeugenbestätigungen", "min": "min", "sec": "sec", "may_delay_comment": "Kann sich aufgrund von ${network} Netzwerküberlastung verzögern.", "address_copied": "<PERSON><PERSON><PERSON> kop<PERSON>t!", "transaction_link_copied": "Transaktionslink kopiert!", "info.features": "DEZENTRALISIERT. MU<PERSON><PERSON>一ASSET. KREUZ<PERSON><PERSON><PERSON>BR<PERSON><PERSON><PERSON>.", "info.summary": "Die sichere, schnelle und sichere Brücke für den Austausch von", "info.summary.next": "Vermögenswerte zwischen IoTeX und dem Blockchain-Universum.", "info.how_iotex_work": "Wie funktioniert ioTube?", "info.chain": "KETTEN", "info.assets": "VERMÖGEN", "info.total_value_locked": "VERMÖGEN INSGESAMT", "faq.what_is_iotube_bridge": "Was ist ioTube Bridge?", "faq.iotube_bridge_is": "ioTube ist eine dezentrale, kettenübergreifende Brücke, die den bidirektionalen Austausch von Krypto-Assets (z.B. fungible/nicht-fungible Token, Stablecoins) zwischen IoTeX und anderen Blockchain-Netzwerken ermöglicht.", "faq.iotube_ui": "ioTube UI ist zugänglich unter", "faq.what_is_iotube_used_for": "Wofür wird ioTube verwendet?", "faq.iotube_used_for.one": "Zusätzlich zu den unmittelbaren Anwendungsfällen für ioTube, um neue Vermögenswerte, Daten und Liquidität in das IoTeX-Netzwerk zu bringen, hat ioTube das Potenzial, die Fähigkeiten des IoTeX-Netzwerks erheblich zu erweitern: Durch die Nutzung von ioTube, um die notwendigen Krypto-Vermögenswerte und Daten zu portieren, hat IoTeX das Potenzial, ein Back-End-Prozessor für ${chain} und andere Blockchains zu werden.", "faq.iotube_used_for.two": "Langfristig können die von der IoTeX-Blockchain generierten einzigartigen Werte, wie z. B. IoT-Gerätedaten, zur Auslösung von Logik in Smart Contracts auf ${chain} und anderen Blockchains verwendet werden. Mit anderen Worten: Die IoTeX-Blockchain könnte als Orakel für Nicht-IoTeX-Verträge dienen und umgekehrt.", "faq.how_does_iotube_work": "Wie funktioniert ioTube?", "faq.iotube_work.two_components": "ioTube besteht aus zwei Kernkomponenten:", "faq.iotube_work.components.smart_contracts": "<PERSON><PERSON> Reihe von intelligenten Verträgen", "faq.iotube_work.components.smart_contracts.work": "vorab auf IoTeX und anderen Blockchains bereitgestellt", "faq.iotube_work.components.pool_of_witness": "Ein Pool von \"Zeugen\"", "faq.iotube_work.components.pool_of_witness.work": "der auf ioTube-Aktionen für Smart Contracts auf IoTeX und anderen Blockchains achtet. Wenn eine Token-Lock-Aktion auf einer Blockchain erkannt wird, validiert der Zeugenpool sie und leitet die endgültigen Informationen an die andere Blockchain weiter: Hier wird die gleiche Menge eines \"Proxy-Tokens\" geprägt. Wird dagegen auf einer Blockchain ein Proxy-Token-Brand festgestellt, validiert der Zeugenpool diesen und leitet die endgültigen Informationen an die andere Blockchain weiter, wo die gleiche Menge des ursprünglichen Tokens entsperrt wird.", "faq.what_is_proxy_token": "Was ist ein Proxy-Token?", "faq.proxy_token_is": "Ein \"Proxy-Token\" ist eine Repräsentation eines anderen Tokens, der auf einer anderen Blockchain lebt (z. B. könnte der ioWETH-Token auf IoTeX die \"Proxy\"-Version des WETH-Tokens auf Ethereum auf IoTeX sein).", "faq.what_different_proxy_token_and_original_token": "Was ist der Unterschied zwischen einem Proxy-Token und dem Original-Token?", "faq.difference_between_proxy_token_and_original_token": "Ein Proxy-Token ist eine exakte Kopie des Original-Tokens, er hat dieselben Eigenschaften und kann auf dieselbe Weise verwendet werden wie der Original-Token: Er lebt nur auf einer anderen Blockchain als der Original-Token.", "faq.does_the_token_supply_increase_when_using_iotube": "<PERSON><PERSON><PERSON><PERSON>ht sich der Tokenvorrat bei der Nutzung von ioTube?", "faq.token_supply_does_not_increase": "Nein: Der Bestand des ursprünglichen Tokens ändert sich durch die Nutzung von ioTube nicht: Die ioTube-Bridge sperrt eine bestimmte Menge eines Tokens auf einer Blockchain (und zieht ihn damit im Wesentlichen aus dem Verkehr) und prägt die exakt gleiche Menge eines Proxy-Tokens auf der anderen Blockchain, der in jeder Hinsicht dem ursprünglichen Token entspricht (d.h. den gesperrten Bestand regeneriert). Das Ergebnis ist, dass der zirkulierende Vorrat des ursprünglichen Tokens derselbe bleibt: Er ist nur auf zwei verschiedene Blockchains statt auf eine aufgeteilt.", "faq.what_happens_to_my_original_tokens_if_i_sell_the_proxy_token": "Was passiert mit meinen Original-Tokens, wenn ich die Proxy-Tokens verkaufe?", "faq.happens_to_original_tokens": "<PERSON><PERSON>d Sie ioTube verwenden, um Ihre ursprünglichen Token von Ethereum auf IoTeX zu übertragen, werden die ursprünglichen Token in den ioTube-Verträgen gespeichert und gesperrt: Sie besitzen diese Token nicht mehr auf Ethereum. Auf der anderen Seite besitzen Sie nun die gleiche Menge an Proxy-Tokens, die Ihnen auf der IoTeX-Blockchain gesendet wird.", "faq.can_i_send_my_proxy_token_back": "Kann ich meine Proxy-Tokens von IoTeX an Ethereum zurücksenden?", "faq.can_send_proxy_token_back": "Ja: <PERSON><PERSON> können jederzeit Proxy-Tokens von IoTeX nach Ethereum senden und erhalten die gleiche Menge des ursprünglichen Tokens auf Ethereum zurück.", "faq.can_i_transfer_as_many_or_limit": "Kann ich so viele Token übertragen, wie ich möchte, oder gibt es ein Limit?", "faq.transfer_limit": "Um ioTube zuverlässig zu starten, haben wir die minimale/maximale Menge des Assets, das verschoben werden kann, begrenzt. Diese Begrenzungen können aufgehoben werden, sobald ioTube mehr Stress validiert wird.", "faq.what_are_the_fees_using_iotube": "Wie hoch sind die Gebühren für die Nutzung von ioTube?", "faq.fee.service": "ioTube-Servicegebühren: 0", "faq.fee.service.desc": "Für die Nutzung des ioTube-Dienstes selbst fallen keine Gebühren an", "faq.fee.from_eth_to_iotex": "Von anderen Blockchains zu IoTeX:", "faq.fee.from_eth_to_iotex.desc.one": "Sie zahlen die reguläre Transaktionsgebühr für das Sperren Ihrer Token auf anderen Blockchains.", "faq.fee.from_eth_to_iotex.desc.two": "<PERSON>e zahlen nicht für die Proxy-Token-Mint-Transaktion auf der IoTeX-Blockchain.", "faq.fee.from_iotex_to_eth": "Von IoTeX zu anderen Blockchains:", "faq.fee.from_iotex_to_eth.desc.one": "<PERSON>e zahlen nicht für die Proxy-Token-Burn-Transaktion auf der IoTeX-Blockchain.", "faq.fee.from_iotex_to_eth.desc.two": "Sie zahlen einen kleinen Betrag an IOTX, um die Gebühren für die Entsperrungstransaktion auf der anderen Blockchain zu decken.", "faq.is_there_is_a_tutorial": "G<PERSON>t es ein Tutorial, das erk<PERSON>, wie man ioTube benutzt?", "faq.tutorial_community": "Ja: <PERSON><PERSON> sich dieses ioTube-Tutorial im IoTeX-Community-Forum an:", "faq.is_iotube_open_source": "Ist ioTube Open Source?", "faq.iotube_github": "Ja: Der ioTube-Code ist quelloffen, <PERSON><PERSON> können ihn auf GitHub finden:", "transaction.status.tips.unknown": "Unbekannt", "transaction.status.tips.created": "Validierung", "transaction.status.tips.submitted": "<PERSON><PERSON> von", "transaction.status.tips.settled": "Abgewickelt", "transaction.status.tips.failed": "Fehlgeschlagen", "from": "<PERSON>", "to": "An", "hash": "Hash", "status": "Status", "asset": "Vermögenswert", "amount": "Betrag", "age": "Alter", "ago": "vor", "day": "Tag", "days": "Tage", "hour": "Stunde", "hours": "Stunden", "just_now": "Läuft", "minute": "Minute", "minutes": "Minuten", "month": "<PERSON><PERSON>", "months": "Monate", "year": "<PERSON><PERSON><PERSON>", "years": "Jahre", "enter_app": "<PERSON><PERSON> e<PERSON>ben", "view_transactions": "ioTube Explorer >>", "tube_v4": "ioTube v5 funktioniert nur mit Web3-Wallets (wie MetaMask, ioPay mobile).", "go_to": "<PERSON><PERSON><PERSON> zu", "switch_network.header": "Zu ${network_name} wechseln?", "switch_network.confirm.content": "Wechsle jetzt zu ${network_name}. Bitte mit ${wallet} oder browserbasierter Wallet bestätigen. ", "button.yes": "<PERSON>a", "button.no": "<PERSON><PERSON>", "account": "Account", "button.logout": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view_iotexscan": "Ansicht auf IoTeXScan", "free": "<PERSON><PERSON><PERSON>", "button.switch": "Wechseln", "button.cancel": "Abbrechen", "ccToken.swap.confirm": "Tausch bestätigen", "button.swap": "Tauschen", "asset.title": "Assets auf ioTube", "asset.supported": "Wird Ihr Token unterstützt?", "asset.start": "<PERSON><PERSON><PERSON> hier > > >", "ethAddress.tooltip": "Dies ist Ihr Web3(0x)-Wallet basierend auf Ihrem privaten Schlüssel. ${keyword}", "iotexAddress.tooltip": "Dies ist Ihr IoTeX-Wallet basierend auf Ihrem privaten Schlüssel. ${keyword}", "my.wallet": "<PERSON><PERSON>", "button.okay": "Okay", "exchange.address.warning": "Achtung! Verwenden Sie keine Tausch- oder Vertragsadressen. Sie können Ihre Token verlieren.", "swap.ctoken.title": "Crosschain Token", "swap.ctoken.description": "Crosschain Token, wie CIOTX, ist ein umhüllter Token, der über iotube mit beliebigen Netzwerken verbunden werden kann. Sie können einen Token jederzeit im Verhältnis 1:1 einpacken oder auspacken. Bitte wechseln Sie zu verschiedenen Netzwerken, um verschiedene Token zu sehen.", "swap.ctoken.not_support": "CTokens sind in den Netzwerken IoTeX und Ethereum verfügbar. Bitte wechseln Sie zu IoTeX oder Etheruem, um fortzufahren.", "home.button.add_iotex_net": "Hinzufügen/Wechseln zum IoTeX-Netzwerk", "home.button.use_third_bridge": "Oder verwenden Sie eine Dritte Brücke:", "button.add_token_metamask": "${token} zu MetaMask hinzufügen", "token.quick_swap": "${tokenA} /${tokenB} tauschen", "wallets.description": "Bitte geben Sie Ihre Wallet-Adresse ein und sehen Sie Ihre Assets auf IoTeX. Für eine vollständige Liste aller Vermögenswerte auf allen von ioTube unterstützten Ketten, besuchen Si<PERSON> bitte ", "metamask_connect_msg": "Bitte verwenden Sie Ihre Brieftasche (MetaMask), um die ausstehende Genehmigung zu bestätigen.", "ledger.connection.description": "Nur IoTeX-Netzwerk", "faq.how_to_bridge_iotexe_title": "Wie kann ich IOTX-E (ERC20 IoTeX Token) überbrücken? ", "faq.how_to_bridge_iotexe_content": "IOTX-E ist der alte IoTeX ERC20 Token auf Ethereum. Nach", "faq.how_to_bridge_iotexe_content1": "wird CIOTX zwischen IoTeX und Ethereum verwendet. Um den IOTX-E-Token in das IoTeX-Netzwerk zu überführen, können Sie ihn konvertieren mit", "faq.how_to_bridge_iotexe_content2": "oder einen anderen DEX, um zuerst auf CIOTX umzusteigen.", "withdraw.warning": "Brücke IOTX?", "withdraw.warning.link": "<PERSON>te zuerst hier einpacken >>", "Wrap": "<PERSON><PERSON><PERSON><PERSON>", "Unwrap": "Auspacken", "Select.Token": "<PERSON><PERSON><PERSON>en Si<PERSON> ein Token", "no.depin.tokens": "<PERSON><PERSON> gefunden.", "All.Tokens": "Alle Token", "DePIN.Tokens": "DePIN-Token", "withdraw": "Auszahlung", "complete.token_on": "Token auf ${chain} :", "button.disconnect": "<PERSON><PERSON><PERSON>", "deposit.polygon_gas_hight": "Warnung: Übertragungen an das Polygon-Netzwerk können sich verzögern, da die aktuellen Gaspreise auf dem Polygon auf 500 Gwei oder mehr gestiegen sind.", "first.use.iotube": "Sie sollten für Ihre erste Nutzung von ioTube einen Airdrop von 0,5 lOTX erhalten, um die Gasgebühren für Ihre erste Transaktion auf loTeX zu decken."}