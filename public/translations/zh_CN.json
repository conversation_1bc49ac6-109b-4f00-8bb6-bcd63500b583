{"hello": "你好${msg}", "wrong.network": "不支持的MetaMask网络", "connect.wallet": "连接钱包", "address.copied": "地址已复制。", "invalid.input": "输入无效", "submit": "提交", "input.insufficient.depositFee": "需要${fee}IOTX作为费用", "input.wallet.not_connected": "钱包未连接", "input.token.unselected": "请选择代币", "input.cashier.invalid": "出纳合约地址无效", "input.swap.invalid": "交换合约地址无效", "input.token.invalid": "代币合约无效", "input.amount.invalid": "无效金额", "input.amount.enter_value": "输入金额", "input.amount.range_error": "金额范围误差", "input.balance.insufficient": "${symbol}余额不足", "input.balance.invalid": "${symbol}余额无效", "input.depositfee.error": "存款费用错误", "input.ioaddress.invalid": "无效的IoTeX地址", "input.crossschainaddress.invalid": "无效的${chain}地址", "deposit": "存款", "approve": "授权", "fee": "费用", "fee.relay": "中继费", "fee.tube": "Tube费用", "fee.network": "网络费用", "relay_to_chain": "中转至${chain}", "confirm": "确认", "you_are_going_to_deposit": "您将要存入", "you_are_going_to_withdraw": "您将要提款", "to_iotube_and_mint": "从${network}并接收", "change_network_eth_warning": "MetaMask不允许我们为您切换到Ethereum。请在MetaMask钱包中进行切换。", "deposit.approving": "授权中", "button.waiting": "等待", "button.confirming": "确认", "complete.broadcast_transaction_successfully": "交易已成功提交", "complete.tx_broadcast_network": "在${network}网络确认${confirmationTimes}次并得到2/3以上见证人的确认后，${amount} ${token}将会发送到您在${chain}上的地址：", "complete.your_tx_chain": "您的 ${chain} TX哈希", "complete.check_status_comment": "您现在可以在以下位置查看状态：", "eta": "ETA", "network_confirmations": "${network}确认", "iotex_confirmations": "IoTeX确认", "witness_confirmation": "证人确认", "min": "分钟", "sec": "秒", "may_delay_comment": "可能由于${network}网络拥堵而延迟。", "address_copied": "地址已复制！", "transaction_link_copied": "交易链接已复制！", "info.features": "去中心化。多资产。跨链桥。", "info.summary": "安全、快速、可靠的交换桥梁", "info.summary.next": "IoTeX与区块链宇宙之间的资产。", "info.how_iotex_work": "ioTube是如何工作的？", "info.chain": "链", "info.assets": "资产", "info.total_value_locked": "总资产", "faq.what_is_iotube_bridge": "什么是ioTube跨链桥?", "faq.iotube_bridge_is": "ioTube是一个去中心化的跨链桥，可以在IoTeX和其他区块链网络之间实现加密资产（如可替代/不可替代代币、稳定币）的双向交换。", "faq.iotube_ui": "可以在以下地址访问ioTube界面", "faq.what_is_iotube_used_for": "ioTube的作用是什么？", "faq.iotube_used_for.one": "ioTube除了能直接为IoTeX网络带来新的资产、数据和流动性的用例之外，还具有极大地扩展IoTeX网络的能力的潜力：通过利用ioTube将必要的加密资产和数据转移过来，IoTeX有潜力成为${chain}和其他区块链的后端处理器。", "faq.iotube_used_for.two": "从长远来看，IoTeX区块链产生的独特资产，如物联网设备数据，可以用来触发${chain}和其他区块链上的智能合约中的逻辑。换句话说，IoTeX的区块链可以作为非IoTeX合约的预言机，反之亦然。", "faq.how_does_iotube_work": "ioTube是如何工作的？", "faq.iotube_work.two_components": "ioTube由两个核心部分组成。", "faq.iotube_work.components.smart_contracts": "智能合约", "faq.iotube_work.components.smart_contracts.work": "已在IoTeX和其他区块链上预先部署", "faq.iotube_work.components.pool_of_witness": "一个\"证人\"池", "faq.iotube_work.components.pool_of_witness.work": "查阅IoTeX和其他区块链上的ioTube智能合约操作。当在一个区块链上检测到代币锁定操作时，证人池会对其进行验证，并将最终确定的信息转发到另一个区块链上，同时在该链上将会铸造相同数量的\"代理代币\"。相反，当在一个区块链上检测到代理代币销毁时，证人池也会对其进行验证，并将最终确定的信息转发到另一个区块链上，同时该链上的相同数量的原始代币将被解锁。", "faq.what_is_proxy_token": "什么是代理代币？", "faq.proxy_token_is": "\"代理代币\"是存在于不同区块链上的代币代表（例如IoTeX上的ioWETH代币是Ethereum上WETH代币在IoTeX上的\"代理\"版本）。", "faq.what_different_proxy_token_and_original_token": "代理代币和原始代币的区别是什么？", "faq.difference_between_proxy_token_and_original_token": "代理代币是原始代币的完全复刻，具有相同的属性，与原始代币具有相同的使用方式：它只是与原始代币存在于不同的区块链上。", "faq.does_the_token_supply_increase_when_using_iotube": "使用ioTube时，是否会增加代币供应量？", "faq.token_supply_does_not_increase": "不会：原始代币的供应量不会因为使用ioTube而发生变化：ioTube跨链桥将一定数量的代币锁定在一个区块链上（本质上是使其退出流通），并在另一个区块链上铸造完全相同数量的代理代币，该代理代币在所有方面都代表原始代币（即重新生成了锁定的供应量）。因此，原始代币的流通供应量将保持不变：只是被分割到两个不同的区块链上，而不是一个。", "faq.what_happens_to_my_original_tokens_if_i_sell_the_proxy_token": "如果我卖出代理代币，我的原始代币会发生什么变化？", "faq.happens_to_original_tokens": "一旦您使用ioTube将原始代币从Ethereum转移到IoTeX，原始代币将被存储并锁定在 ioTube 合约中：您将不再拥有Ethereum上的这些代币。另一方面，您将拥有IoTeX区块链上发送给您的等值的代理代币。", "faq.can_i_send_my_proxy_token_back": "我可以将代理代币从IoTeX发送回Ethereum吗？", "faq.can_send_proxy_token_back": "是的：您可以随时将代理代币从IoTeX发送到Ethereum，并在Ethereum上接收到相同数量的原始代币。", "faq.can_i_transfer_as_many_or_limit": "是否存在转让额度限制？", "faq.transfer_limit": "为了更可靠地启动ioTube，我们限制了可移动资产的最小/最大金额。 当ioTube的性能经过更多验证之后，就可以解除这些限制。", "faq.what_are_the_fees_using_iotube": "使用ioTube需要支付哪些费用？", "faq.fee.service": "ioTube服务费：0", "faq.fee.service.desc": "使用ioTube服务本身不收取任何费用", "faq.fee.from_eth_to_iotex": "从其他区块链到IoTeX链：", "faq.fee.from_eth_to_iotex.desc.one": "您将支付常规交易费用，以在其他区块链上锁定您的代币。", "faq.fee.from_eth_to_iotex.desc.two": "您无需为IoTeX链上的代理代币的铸币交易付费。", "faq.fee.from_iotex_to_eth": "从IoTeX链到其他区块链：", "faq.fee.from_iotex_to_eth.desc.one": "您无需为IoTeX链上的代理代币的销毁交易付费。", "faq.fee.from_iotex_to_eth.desc.two": "您将支付少量的IOTX作为在另一区块链上解锁交易的费用。", "faq.is_there_is_a_tutorial": "是否有教程指导如何使用ioTube？", "faq.tutorial_community": "是的：在IoTeX社区论坛上查看ioTube教程：", "faq.is_iotube_open_source": "ioTube是否开源？", "faq.iotube_github": "是的：ioTube代码是开源的，您可以在GitHub上找到它：", "transaction.status.tips.unknown": "未知", "transaction.status.tips.created": "正在验证", "transaction.status.tips.submitted": "发送中", "transaction.status.tips.settled": "已完成", "transaction.status.tips.failed": "失败", "from": "从", "to": "至", "hash": "交易哈希", "status": "状态", "asset": "资产", "amount": "数量", "age": "时间", "ago": "前", "day": "天", "days": "天", "hour": "小时", "hours": "小时", "just_now": "刚刚", "minute": "分钟", "minutes": "分钟", "month": "月", "months": "月", "year": "年", "years": "年", "enter_app": "进入应用", "view_transactions": "ioTube 浏览器 >>", "tube_v4": "ioTube v5仅适用于 Web3 钱包（如 MetaMask、ioPay mobile）。", "go_to": "转到", "switch_network.header": "确认切换到${network_name}网络吗？", "switch_network.confirm.content": "正在切换到 ${network_name} 网络。请在 ${wallet} 或基于浏览器的钱包上确认。", "button.yes": "是", "button.no": "否", "account": "账户", "button.logout": "退出登录", "view_iotexscan": "在IoTeXScan上查看", "free": "免费", "button.switch": "切换", "button.cancel": "取消", "ccToken.swap.confirm": "确认兑换", "button.swap": "兑换", "asset.title": "ioTube上的资产", "asset.supported": "是否需要使您的代币获得支持？", "asset.start": "从这里开始 >>>", "ethAddress.tooltip": "这是基于您的私钥的Web3(0x)钱包。${keyword}", "iotexAddress.tooltip": "这是基于您的私钥的IoTeX钱包。${keyword}", "my.wallet": "我的钱包", "button.okay": "确定", "exchange.address.warning": "警告：请勿使用交易地址或合约地址。否则您可能会丢失您的代币。", "swap.ctoken.title": "跨链代币", "swap.ctoken.description": "跨链代币，如CIOTX，是一种可以通过iotube桥接到任何网络的封装代币。您可以随时以1:1的比例封装或解封代币。请切换到不同的网络以查看不同的代币。", "swap.ctoken.not_support": "跨链代币（CTokens）在IoTeX和Ethereum网络上可用。请切换到IoTeX或Ethereum网络以继续。", "home.button.add_iotex_net": "添加/切换到IoTeX网络", "home.button.use_third_bridge": "或使用第三方桥接器:", "button.add_token_metamask": "将 ${token} 添加到MetaMask", "token.quick_swap": "兑换 ${tokenA}/${tokenB}", "wallets.description": "请输入您的钱包地址，查看您在IoTeX上的资产。要查看ioTube支持的所有链上的所有资产的完整列表，请访问 ", "metamask_connect_msg": "请使用您的钱包（MetaMask）批准待处理的权限。", "ledger.connection.description": "仅限IoTeX网络", "faq.how_to_bridge_iotexe_title": "我该如何桥接IOTX-E（ERC20 IoTeX代币）？", "faq.how_to_bridge_iotexe_content": "IOTX-E是Ethereum网络上的传统IoTeX ERC20代币。之后", "faq.how_to_bridge_iotexe_content1": "，CIOTX将在IoTeX和Ethereum网络之间使用。为了将IOTX-E代币桥接到IoTeX网络，您可以使用：", "faq.how_to_bridge_iotexe_content2": "或者通过其他DEX先兑换成CIOTX。", "withdraw.warning": "跨链IOTX？", "withdraw.warning.link": "请先在此处封装>>", "Wrap": "封装", "Unwrap": "解封", "Select.Token": "选择一个代币", "no.depin.tokens": "未找到结果。", "All.Tokens": "所有代币", "DePIN.Tokens": "DePIN代币", "withdraw": "提款", "complete.token_on": "${chain} 上的代币：", "button.disconnect": "断开连接", "deposit.polygon_gas_hight": "警告：由于Polygon网络上的当前Gas价格已经上升到500 gwei或更高，因此向Polygon网络的转账可能会延迟。", "first.use.iotube": "首次使用ioTube(在IOTEX网络上您的钱包IOTX余额为0且Nonce为0)时，您将收到 0.5 IOTX 空投，用于支付您在 loTeX 上首次交易的Gas费用。"}