{"hello": "bonjour ${msg}", "wrong.network": "R<PERSON>eau MetaMask non pris en charge", "connect.wallet": "Connect<PERSON> le portefeuille", "address.copied": "L'adresse est copiée.", "invalid.input": "En<PERSON><PERSON> invalide", "submit": "So<PERSON><PERSON><PERSON>", "input.insufficient.depositFee": "Besoin de ${fee} IOTX pour les frais", "input.wallet.not_connected": "Le portefeuille n'est pas connecté", "input.token.unselected": "Veuillez sélectionner un jeton", "input.cashier.invalid": "Adresse invalide du contrat de caisse", "input.swap.invalid": "Adresse du contrat de swap invalide", "input.token.invalid": "Contrat de jeton invalide", "input.amount.invalid": "Montant non valide", "input.amount.enter_value": "<PERSON><PERSON><PERSON> le montant", "input.amount.range_error": "Erreur dans la fourchette des montants", "input.balance.insufficient": "Solde ${symbol} insuffisant", "input.balance.invalid": "Solde ${symbol} non valide", "input.depositfee.error": "Erreur dans les frais de dépôt", "input.ioaddress.invalid": "Adresse IoTeX invalide", "input.crossschainaddress.invalid": "Adresse ${chain} invalide", "deposit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "approve": "Approuver", "fee": "Honoraires", "fee.relay": "<PERSON><PERSON>", "fee.tube": "Frais de tube", "fee.network": "<PERSON><PERSON> r<PERSON>", "relay_to_chain": "<PERSON><PERSON>s vers ${chain}", "confirm": "Confirmer", "you_are_going_to_deposit": "Vous allez déposer", "you_are_going_to_withdraw": "Vous allez vous retirer", "to_iotube_and_mint": "de ${network} et recevez", "change_network_eth_warning": "MetaMask ne nous permet pas de passer à Ethereum à votre place. Veuillez basculer dans MetaMask.", "deposit.approving": "Approuver", "button.waiting": "Attente", "button.confirming": "Confirmation", "complete.broadcast_transaction_successfully": "Transaction soumise avec succès", "complete.tx_broadcast_network": "Après ${confirmationTimes} confirmation du réseau ${network} et confirmations supplémentaires de 2/3 témoins, ${amount} ${token} sera envoyé à votre adresse sur ${chain} :", "complete.your_tx_chain": "Votre ${chain} Hachage TX", "complete.check_status_comment": "Vous pouvez vérifier l'état d'avancement dès maintenant dans", "eta": "ETA", "network_confirmations": "${network} confirmations", "iotex_confirmations": "Confirmations IoTeX", "witness_confirmation": "Confirmations de témoins", "min": "min", "sec": "sec", "may_delay_comment": "Peut être retardé en raison de la congestion du réseau ${network}.", "address_copied": "Adresse copiée !", "transaction_link_copied": "Lien de transaction copié !", "info.features": "DÉCENTRALISÉ. MULTI一ASSET. PONT À CHAÎNE CROISÉE.", "info.summary": "La passerelle sûre, rapide et sécurisée pour l'échange d'informations", "info.summary.next": "entre IoTeX et l'univers de la blockchain.", "info.how_iotex_work": "Comment fonctionne ioTube", "info.chain": "CHAÎNES", "info.assets": "ACTIF", "info.total_value_locked": "TOTAL DE L'ACTIF", "faq.what_is_iotube_bridge": "Qu'est-ce que ioTube Bridge ?", "faq.iotube_bridge_is": "ioTube est un pont décentralisé inter-chaînes qui permet l'échange bidirectionnel de crypto-actifs (par exemple, des jetons fongibles/non fongibles, des stablecoins) entre IoTeX et d'autres réseaux de blockchain.", "faq.iotube_ui": "L'interface utilisateur ioTube est accessible à l'adresse suivante", "faq.what_is_iotube_used_for": "À quoi sert ioTube ?", "faq.iotube_used_for.one": "Outre les cas d'utilisation immédiats d'ioTube pour apporter de nouveaux actifs, données et liquidités au réseau IoTeX, ioTube a le potentiel d'étendre considérablement les capacités du réseau IoTeX : en utilisant ioTube pour porter les crypto-actifs et les données nécessaires, IoTeX a le potentiel de devenir un processeur back-end pour ${chain} et d'autres blockchains.", "faq.iotube_used_for.two": "À long terme, les actifs uniques générés par la blockchain IoTeX, tels que les données des appareils IoT, peuvent être utilisés pour déclencher la logique dans les contrats intelligents sur ${chain} et d'autres blockchains. En d'autres termes, la blockchain IoTeX pourrait servir d'oracle pour les contrats non IoTeX, et vice-versa.", "faq.how_does_iotube_work": "Comment fonctionne ioTube ?", "faq.iotube_work.two_components": "ioTube est composé de deux éléments principaux :", "faq.iotube_work.components.smart_contracts": "Un ensemble de contrats intelligents", "faq.iotube_work.components.smart_contracts.work": "pré-déployé sur IoTeX et d'autres blockchains", "faq.iotube_work.components.pool_of_witness": "Un pool de \"témoins\"", "faq.iotube_work.components.pool_of_witness.work": "qui est à l'écoute des actions ioTube sur les contrats intelligents sur IoTeX et d'autres blockchains. Lorsqu'une action de blocage de jeton est détectée sur une blockchain, le pool de témoins la valide et transmet l'information finalisée à l'autre blockchain : ici, la même quantité de \"Proxy-Token\" est frappée. À l'inverse, lorsqu'un Proxy-Token burn est détecté sur une blockchain, le pool de témoins le valide et transmet l'information finalisée à l'autre blockchain, où le même montant du token original est déverrouillé.", "faq.what_is_proxy_token": "Qu'est-ce qu'un Proxy-Token ?", "faq.proxy_token_is": "Un \"Proxy-Token\" est une représentation d'un autre token qui vit sur une blockchain différente (par exemple, le token ioWETH sur IoTeX pourrait être la version \"Proxy\" sur IoTeX du token WETH sur Ethereum).", "faq.what_different_proxy_token_and_original_token": "Quelle est la différence entre un Proxy-Token et le token original ?", "faq.difference_between_proxy_token_and_original_token": "Un Proxy-Token est une copie exacte du token original, il a les mêmes propriétés et peut être utilisé de la même manière que le token original : il vit simplement sur une blockchain différente de celle de l'original.", "faq.does_the_token_supply_increase_when_using_iotube": "L'offre de jetons augmente-t-elle lorsque l'on utilise ioTube ?", "faq.token_supply_does_not_increase": "Non : l'offre du jeton original ne change jamais suite à l'utilisation de ioTube : le pont ioTube verrouille une certaine quantité de jeton sur une blockchain (essentiellement en le retirant de la circulation) et frappe exactement la même quantité d'un Proxy-Token sur l'autre blockchain, qui représente à tous égards le jeton original (c'est-à-dire qu'il régénère l'offre verrouillée). Par conséquent, l'offre en circulation du jeton d'origine reste la même : elle est simplement répartie sur deux blockchains différentes au lieu d'une.", "faq.what_happens_to_my_original_tokens_if_i_sell_the_proxy_token": "Qu'advient-il de mes jetons originaux si je vends les jetons de procuration ?", "faq.happens_to_original_tokens": "Une fois que vous utilisez ioTube pour transférer vos jetons originaux d'Ethereum à IoTeX, les jetons originaux sont stockés et verrouillés dans les contrats ioTube : vous ne possédez plus ces jetons sur Ethereum. En revanche, vous possédez désormais la même quantité de Proxy-Tokens qui vous est envoyée sur la blockchain IoTeX.", "faq.can_i_send_my_proxy_token_back": "Pui<PERSON>-je renvoyer mes Proxy-Tokens de IoTeX vers Ethereum ?", "faq.can_send_proxy_token_back": "Oui : vous pouvez envoyer des Proxy-Tokens d'IoTeX vers Ethereum à tout moment, et recevoir en retour la même quantité du token original sur Ethereum.", "faq.can_i_transfer_as_many_or_limit": "Puis-je transférer autant de jetons que je le souhaite ou y a-t-il une limite ?", "faq.transfer_limit": "Pour lancer ioTube de manière fiable, nous avons limité la quantité minimale/maximale de l'actif qui peut être déplacé. Ces limites pourront être levées une fois que ioTube aura été validé par un plus grand nombre de tests.", "faq.what_are_the_fees_using_iotube": "Quels sont les frais d'utilisation de ioTube ?", "faq.fee.service": "Frais de service ioTube : 0", "faq.fee.service.desc": "Il n'y a aucun frais pour l'utilisation du service ioTube lui-même", "faq.fee.from_eth_to_iotex": "Des autres blockchains à IoTeX :", "faq.fee.from_eth_to_iotex.desc.one": "Vous paierez les frais de transaction habituels pour verrouiller vos jetons sur d'autres blockchains.", "faq.fee.from_eth_to_iotex.desc.two": "Vous ne paierez pas pour la transaction Proxy-Token sur la blockchain IoTeX.", "faq.fee.from_iotex_to_eth": "De IoTeX à d'autres blockchains :", "faq.fee.from_iotex_to_eth.desc.one": "Vous ne paierez pas pour la transaction de brûlage de Proxy-Token sur la blockchain IoTeX.", "faq.fee.from_iotex_to_eth.desc.two": "Vous paierez un petit montant d'IOTX pour couvrir les frais de la transaction de déverrouillage sur l'autre blockchain.", "faq.is_there_is_a_tutorial": "Existe-t-il un didacticiel expliquant comment utiliser ioTube ?", "faq.tutorial_community": "Oui : consultez ce tutoriel ioTube sur le forum de la communauté IoTeX :", "faq.is_iotube_open_source": "ioTube est-il open source ?", "faq.iotube_github": "Oui : le code d'ioTube est open source, vous pouvez le trouver sur GitHub :", "transaction.status.tips.unknown": "Inconnu", "transaction.status.tips.created": "Valider", "transaction.status.tips.submitted": "Envoi", "transaction.status.tips.settled": "Réglé", "transaction.status.tips.failed": "Manquée", "from": "À partir de", "to": "Pour", "hash": "<PERSON><PERSON>", "status": "Statut", "asset": "Actif", "amount": "<PERSON><PERSON>", "age": "Âge", "ago": "il y a", "day": "journ<PERSON>", "days": "journ<PERSON>", "hour": "heure", "hours": "heures", "just_now": "tout à l' heure", "minute": "minute", "minutes": "minutes", "month": "mois", "months": "mois", "year": "ann<PERSON>", "years": "ann<PERSON>", "enter_app": "Entrer dans l'application", "view_transactions": "ioTube Explorer >>", "tube_v4": "ioTube v5 ne fonctionne qu'avec les portefeuilles Web3 (comme MetaMask, ioPay mobile).", "go_to": "<PERSON><PERSON>", "switch_network.header": "Passage à ${network_name} ?", "switch_network.confirm.content": "Passer à ${network_name} maintenant. Veuillez confirmer sur ${wallet} ou sur le portefeuille basé sur un navigateur.", "button.yes": "O<PERSON>", "button.no": "Non", "account": "<PERSON><PERSON><PERSON>", "button.logout": "Déconnexion", "view_iotexscan": "Voir sur IoTeXScan", "free": "<PERSON><PERSON><PERSON>", "button.switch": "Interrupteur", "button.cancel": "Annuler", "ccToken.swap.confirm": "Confirmer l'échange", "button.swap": "<PERSON><PERSON><PERSON>", "asset.title": "Atouts sur ioTube", "asset.supported": "Obtenir un soutien pour votre jeton ?", "asset.start": "Commencez ici >>>", "ethAddress.tooltip": "Il s'agit de votre portefeuille Web3(0x) basé sur votre clé privée. ${keyword}", "iotexAddress.tooltip": "Il s'agit de votre portefeuille IoTeX basé sur votre clé privée. ${keyword}", "my.wallet": "Mon portefeuille", "button.okay": "D'accord", "exchange.address.warning": "Avertissement : N'utilisez pas d'adresses d'échange ou d'adresses de contrat. V<PERSON> risquez de perdre vos jetons.", "swap.ctoken.title": "Token Crosschain", "swap.ctoken.description": "Les jetons Crosschain, tels que CIOTX, sont des jetons enveloppés qui peuvent être connectés à n'importe quel réseau via iotube. Vous pouvez envelopper ou désenvelopper un jeton dans un rapport 1:1 à tout moment. Veuillez changer de réseau pour voir les différents tokens.", "swap.ctoken.not_support": "Les CTokens sont disponibles sur les réseaux IoTeX et Ethereum. Veuillez passer à IoTeX ou Etheruem pour continuer.", "home.button.add_iotex_net": "Ajouter/commuter vers le réseau IoTeX", "home.button.use_third_bridge": "Ou utiliser un tiers pont:", "button.add_token_metamask": "Ajouter ${token} au MetaMask", "token.quick_swap": "Échanger ${tokenA}/${tokenB}", "wallets.description": "Veuillez saisir l'adresse de votre portefeuille et voir vos actifs sur IoTeX. Pour obtenir une liste complète de tous les actifs sur toutes les chaînes prises en charge par ioTube, veuillez consulter le site suivant ", "metamask_connect_msg": "Veuillez utiliser votre portefeuille (MetaMask) pour approuver la permission en attente.", "ledger.connection.description": "Réseau IoTeX uniquement", "faq.how_to_bridge_iotexe_title": "Comment puis-je faire le pont avec l'IOTX-E (ERC20 IoTeX Token) ? ", "faq.how_to_bridge_iotexe_content": "IOTX-E est l'ancien jeton IoTeX ERC20 sur Ethereum. Après", "faq.how_to_bridge_iotexe_content1": "CIOTX sera utilisé entre IoTeX et Ethereum. Afin de relier le jeton IOTX-E au réseau IoTeX, vous pouvez le convertir à l'aide de la fonction", "faq.how_to_bridge_iotexe_content2": "ou d'un autre DEX pour passer d'abord au CIOTX.", "withdraw.warning": "Pont IOTX ?", "withdraw.warning.link": "Veuillez d'abord emballer ici >>", "Wrap": "Envelopper", "Unwrap": "<PERSON><PERSON><PERSON><PERSON>", "Select.Token": "Sélectionnez un jeton", "no.depin.tokens": "Aucun résultat trouvé.", "All.Tokens": "Tous les jetons", "DePIN.Tokens": "Jetons DePIN", "withdraw": "<PERSON><PERSON><PERSON>", "complete.token_on": "Jeton sur ${chain} :", "button.disconnect": "Déconnecter", "deposit.polygon_gas_hight": "Attention : les transferts vers le réseau Polygon peuvent être retardés, car les prix actuels du gaz sur le Polygon ont augmenté jusqu'à 500 gwei ou plus.", "first.use.iotube": "Vous devriez recevoir un airdrop de 0,5 lOTX pour votre première utilisation d'ioTube afin de couvrir les frais de gaz pour votre transaction initiale sur loTeX."}