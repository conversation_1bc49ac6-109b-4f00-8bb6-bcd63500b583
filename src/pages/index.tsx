import RootLayout from "@/layouts/RootLayout";
import { tabSelect, tabUnSelect } from "@/components/primitives.ts";
import { Button, Image } from "@heroui/react";
import Bridge from "@/components/Bridge";

export default function IndexPage() {
  return (
    <RootLayout>
      <section className="flex flex-col items-center justify-center pt-5">
        <div className="flex items-center justify-between w-full pb-5">
          <div>
            <Button className={tabSelect()} radius="full">
              Bridge
            </Button>
            <Button className={tabUnSelect()} radius="full">
              Buy
            </Button>
          </div>
          <div className="flex">
            <Image className="size-6" src="/public/icon_history.svg" />
            <Image className="size-6 ml-3" src="/public/icon_setting.svg" />
          </div>
        </div>
        <Bridge />
      </section>
    </RootLayout>
  );
}
