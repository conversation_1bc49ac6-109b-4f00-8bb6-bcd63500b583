import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Tab,
  Image,
} from "@heroui/react";
import Token from "@/types/token.ts";

export type TokenSelectorProps = {
  isOpen: boolean;
  onOpenChange: () => void;
  tokens: Token[];
};

export default function TokenSelector(props: TokenSelectorProps) {
  console.log(props.tokens.length);

  return (
    <>
      <Modal
        className="min-w-[32rem]"
        isOpen={props.isOpen}
        onOpenChange={props.onOpenChange}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader className="text-xl font-semibold text-color8">
                Select Token
              </ModalHeader>
              <ModalBody>
                <Tabs
                  classNames={{
                    tabList:
                      "gap-6 w-full relative rounded-none p-0 border-b border-divider",
                    cursor: "w-full bg-purple1",
                    tab: "max-w-fit px-0 h-12",
                    tabContent:
                      "group-data-[selected=true]:text-purple1 text-color7 font-medium",
                  }}
                  size="md"
                  variant="underlined"
                >
                  <Tab key="photos" title="Popular Tokens">
                    <div className="h-80 overflow-y-auto custom-scrollbar">
                      {props.tokens.map((token) => (
                        <div
                          key={token.id}
                          className="flex items-center w-full py-4 px-2 hover:bg-color3 rounded-lg transition-colors cursor-pointer"
                        >
                          <Image
                            className="size-8 rounded-full"
                            fallbackSrc="/public/images/icon_coin.svg"
                            src={token.logouri}
                          />
                          <div className="flex flex-col items-start ml-4 flex-1">
                            <div className="text-base font-medium text-color8">
                              {token.symbol}
                            </div>
                            <div className="text-sm text-color7 mt-1">
                              {token.name}
                            </div>
                          </div>
                          <div className="text-base font-medium text-color8 mr-4">
                            1999
                          </div>
                          <Image
                            className="size-6 mx-3 opacity-70 hover:opacity-100 transition-opacity"
                            src="/public/images/logo_metamask.svg"
                          />
                          <Image
                            className="size-6 opacity-70 hover:opacity-100 transition-opacity"
                            src="/public/images/icon_external_link.svg"
                          />
                        </div>
                      ))}
                    </div>
                  </Tab>
                  <Tab key="music" title="Depin Tokens" />
                  <Tab key="videos" title="All Tokens" />
                </Tabs>
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </>
  );
}
