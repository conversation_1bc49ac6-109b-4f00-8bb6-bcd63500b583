import clsx, { ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export const ellipseStr = (text: string, length = 10) => {
  if (text.length <= length) {
    return text;
  }
  return text.slice(0, length) + "..." + text.slice(-length - 1);
};

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 格式化余额显示
 * @param value - 要格式化的数值（字符串或数字）
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 *
 * 规则：
 * - 整数部分使用千分位格式化
 * - 当值 < 1 时，小数部分保留四位有效数字
 * - 当值 >= 1 时，小数部分保留4位
 * - 去掉小数末尾的0
 *
 * @example
 * formatBalance("1234.56789") // "1,234.5679"
 * formatBalance("0.00012345") // "0.0001235"
 * formatBalance("0.1234567") // "0.1235"
 * formatBalance("1000") // "1,000"
 * formatBalance("1000.0000") // "1,000"
 */
export function formatBalance(
  value: string | number,
  options: {
    /** 当值小于1时的有效数字位数，默认4位 */
    significantDigitsForSmall?: number;
    /** 当值大于等于1时的小数位数，默认4位 */
    decimalPlacesForLarge?: number;
    /** 是否显示千分位分隔符，默认true */
    useThousandsSeparator?: boolean;
  } = {},
): string {
  const {
    significantDigitsForSmall = 4,
    decimalPlacesForLarge = 4,
    useThousandsSeparator = true,
  } = options;

  if (value === null || value === undefined || value === "") {
    return "0";
  }

  const numValue = typeof value === "string" ? parseFloat(value) : value;

  if (isNaN(numValue) || !isFinite(numValue)) {
    return "0";
  }

  if (numValue === 0) {
    return "0";
  }

  const isNegative = numValue < 0;
  const absValue = Math.abs(numValue);

  let formattedValue: string;

  if (absValue < 1) {
    if (absValue < 1e-6) {
      const exponent = Math.floor(Math.log10(absValue));
      const mantissa = absValue / Math.pow(10, exponent);
      const roundedMantissa = parseFloat(
        mantissa.toPrecision(significantDigitsForSmall),
      );
      const result = roundedMantissa * Math.pow(10, exponent);
      formattedValue = result.toFixed(
        -exponent + significantDigitsForSmall - 1,
      );
    } else {
      formattedValue = absValue.toPrecision(significantDigitsForSmall);
      const num = parseFloat(formattedValue);
      formattedValue = num.toString();
    }
  } else {
    formattedValue = absValue.toFixed(decimalPlacesForLarge);
  }

  formattedValue = formattedValue.replace(/\.?0+$/, "");

  if (useThousandsSeparator) {
    const parts = formattedValue.split(".");
    const integerPart = parts[0];
    const decimalPart = parts[1];

    const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ",");

    formattedValue = decimalPart
      ? `${formattedInteger}.${decimalPart}`
      : formattedInteger;
  }

  return isNegative ? `-${formattedValue}` : formattedValue;
}
