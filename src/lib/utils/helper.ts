import numeral from "numeral";
import BN from "bignumber.js";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import utc from "dayjs/plugin/utc";
import { validateEthAddress } from "@/components/Address";
import { fromBytes, fromString } from "@iotexproject/iotex-address-ts";
import { validateAddress } from "iotex-antenna/lib/account/utils";

dayjs.extend(relativeTime);
dayjs.extend(utc);

export const helper = {
  promise: {
    async sleep(ms) {
      return new Promise((resolve) => setTimeout(resolve, ms));
    },
    async runAsync<T, U = Error>(
      promise: Promise<T>,
    ): Promise<[U | null, T | null]> {
      return promise
        .then<[null, T]>((data: T) => [null, data])
        .catch<[U, null]>((err) => [err, null]);
    },
  },
  json: {
    safeParse(val: any) {
      try {
        return JSON.parse(val);
      } catch (error) {
        return val;
      }
    },
  },
  string: {
    toFixString(str, length) {
      if (str && str.length > length) {
        return str.substr(0, length) + "...";
      } else {
        return str;
      }
    },
    truncate(fullStr, strLen, separator) {
      if (fullStr.length <= strLen) return fullStr;

      separator = separator || "...";

      var sepLen = separator.length,
        charsToShow = strLen - sepLen,
        frontChars = Math.ceil(charsToShow / 2),
        backChars = Math.floor(charsToShow / 2);

      return (
        fullStr.substr(0, frontChars) +
        separator +
        fullStr.substr(fullStr.length - backChars)
      );
    },
  },
  number: {
    numberWithCommas(x: number) {
      var parts = x.toString().split(".");
      parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ",");
      return parts.join(".");
    },
    countNonZeroNumbers: (str: string) => {
      let index = 0;
      const length = str.length;
      for (
        ;
        index < length && (str[index] === "0" || str[index] === ".");
        index += 1
      );
      return length - index - Number(str.includes("."));
    },
    toPrecisionFloor: (
      str: number | string,
      options?: {
        decimals?: number;
        format?: string;
        toLocalString?: boolean;
      },
    ) => {
      const {
        decimals = 6,
        format = "",
        toLocalString = false,
      } = options || {};
      if (!str || isNaN(Number(str))) return "";

      if (helper.number.countNonZeroNumbers(String(str)) <= decimals)
        return String(str);
      const numStr = new BN(str).toFixed();
      let result = "";
      let index = 0;
      const numLength = numStr.length;
      for (; numStr[index] === "0" && index < numLength; index += 1);

      if (index === numLength) return "0";

      if (numStr[index] === ".") {
        // number < 0
        result = "0";
        for (
          ;
          (numStr[index] === "0" || numStr[index] === ".") && index < numLength;
          index += 1
        ) {
          result = result + numStr[index];
        }
      }
      let resultNumLength = 0;
      for (
        ;
        index < numLength &&
        (resultNumLength < decimals || !result.includes("."));
        index += 1
      ) {
        result = result + numStr[index];

        if (numStr[index] !== ".") resultNumLength += 1;
      }
      if (format) {
        return numeral(Number(result)).format(format);
      }

      const decimalPart = numStr.toString().match(/\.(\d+)/);
      if (!decimalPart) {
        return toLocalString
          ? Number(result).toLocaleString()
          : new BN(result).toFixed();
      }

      let decimalPartStr = decimalPart[1];
      const decimalPartLen = decimalPartStr.length;
      //fix bug when number  like 0.000000001 toPrecisionFloor(6) = 0.000000
      let zeroLenStr = "";
      for (let i = 0; i < decimals; i += 1) {
        zeroLenStr = zeroLenStr + "0";
      }
      if (decimalPartLen > decimals) {
        if (decimalPartStr.indexOf(zeroLenStr) != 0) {
          decimalPartStr = decimalPartStr.slice(0, decimals);
        }
      }
      return (
        (toLocalString
          ? Number(result.toString().replace(/\.(\d+)/, "")).toLocaleString()
          : new BN(result.toString().replace(/\.(\d+)/, ""))) +
        "." +
        decimalPartStr
      );
    },
    getBN: (value: number | string | BN) => {
      return value instanceof BN
        ? value
        : typeof value === "string"
          ? new BN(Number(value))
          : new BN(value);
    },
  },
  time: {
    fromNow(ts: string): string {
      if (!ts || dayjs.utc(ts).valueOf() <= 0) {
        return "--";
      }

      return dayjs.utc(ts).fromNow();
    },

    translateFn(ts: string): string {
      const keyMessage = [
        "years",
        "year",
        "months",
        "month",
        "days",
        "day",
        "hours",
        "hour",
        "minutes",
        "minute",
        "ago",
        "just now",
      ];
      let text = this.fromNow(ts);
      keyMessage.map((value) => {
        text = text.replace(
          value,
          (window as any)._store.lang.t(`${value.replace(" ", "")}`),
        );
      });
      if (
        text.includes((window as any)._store.lang.t(`hour`)) ||
        text.includes((window as any)._store.lang.t(`hours`)) ||
        text.includes((window as any)._store.lang.t(`day`)) ||
        text.includes((window as any)._store.lang.t(`days`)) ||
        text.includes((window as any)._store.lang.t(`month`)) ||
        text.includes((window as any)._store.lang.t(`months`)) ||
        text.includes((window as any)._store.lang.t(`year`)) ||
        text.includes((window as any)._store.lang.t(`years`))
      ) {
        return text.replace(/^an?/, "1");
      }
      return text.replace(/^an?/, "a");
    },
  },
  address: {
    convertAddress(address: any) {
      try {
        if (validateEthAddress(address)) {
          return fromBytes(
            Buffer.from(String(address).replace(/^0x/, ""), "hex"),
          ).string();
        }
        if (validateAddress(address)) {
          return fromString(address).stringEth();
        }
        return address;
      } catch (error) {
        return address;
      }
    },
  },
};
