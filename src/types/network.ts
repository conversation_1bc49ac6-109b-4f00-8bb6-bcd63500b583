import { makeObservable } from "mobx";

export abstract class Network {
  name: string = "";
  fullName: string = "";
  chainId: number = 0;
  logoUrl: string = "";
  rpcUrl: string = "";
  explorerName: string = "";
  explorerURL: string = "";
  destNetworks: Network[] = [];

  constructor(config: Partial<Network>) {
    Object.assign(this, config);
    makeObservable(this);
  }

  abstract isAddressValid(address: string): boolean;
}
