import BigNumber from "bignumber.js";
import { makeAutoObservable } from "mobx";
import { helper } from "@/lib/utils/helper.ts";

export class BigNumberState {
  value: BigNumber = new BigNumber(0);
  loading: boolean = false;
  decimals = 18;
  fixed = 6;
  formatter?: Function;

  constructor(args: Partial<BigNumberState>) {
    Object.assign(this, args);
    makeAutoObservable(this);
  }

  get format() {
    if (this.loading) return "...";
    return this.getFormat();
  }

  get formatWithCommas() {
    if (this.loading) return "...";
    return helper.number.numberWithCommas(this.getFormat());
  }

  get formatShort() {
    if (this.loading) return "...";
    let amount = this.getFormat();
    let newAmount = Number(amount.toString()).toFixed(5).slice(0, -1);
    return Number(newAmount).toString();
  }

  getFormat({
    decimals = this.decimals,
    fixed = this.fixed,
  }: { decimals?: number; fixed?: number } = {}) {
    if (this.loading) return "...";
    if (this.formatter) return this.formatter(this);
    return helper.number.toPrecisionFloor(
      new BigNumber(this.value).dividedBy(10 ** decimals).toFixed(),
      {
        decimals: fixed,
      },
    );
  }

  setDecimals(decimals: number) {
    this.decimals = decimals;
  }

  setValue(value: BigNumber) {
    this.value = value;
    this.setLoading(false);
  }

  setLoading(loading: boolean) {
    this.loading = loading;
  }
}
