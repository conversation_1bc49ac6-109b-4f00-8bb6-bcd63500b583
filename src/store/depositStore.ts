import { makeAutoObservable, reaction } from "mobx";
import { Network } from "@/types/network.ts";
import { networks } from "@/config/network.ts";
import { rootStore } from "@/store/index.ts";

export class DepositStore {
  fromNetwork: Network;
  destNetwork: Network;

  receiptAddress: string | undefined;

  constructor() {
    this.fromNetwork = networks[0] || null;
    this.destNetwork = networks[1] || null;

    makeAutoObservable(this);

    this.watch();
  }

  private watch() {
    reaction(
      () => ({
        fromNetwork: this.fromNetwork,
        destNetwork: this.destNetwork,
      }),
      ({ fromNetwork, destNetwork }) => {
        if (fromNetwork && destNetwork) {
          rootStore.tokenStore.getTokenList(
            fromNetwork.chainId,
            destNetwork.chainId,
          );
        }
      },
      {
        fireImmediately: true, // 初始化时立即执行一次
      },
    );
  }

  setFromNetwork(network: Network) {
    this.fromNetwork = network;
    this.destNetwork = network.destNetworks[0];
  }

  setDestNetwork(network: Network) {
    this.destNetwork = network;
  }

  swapNetworks() {
    const temp = this.fromNetwork;
    this.fromNetwork = this.destNetwork;
    this.destNetwork = temp;
  }

  setReceiptAddress(address: string | undefined) {
    this.receiptAddress = address;
  }
}
