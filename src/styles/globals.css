/*@import "tailwindcss";*/

@tailwind base;
@tailwind components;
@tailwind utilities;

@font-face {
    font-family: 'RedHatDisplay';
    src: url('/public/fonts/RedHatDisplay.ttf') format('ttf');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

body {
    font-family: 'RedHatDisplay', sans-serif;
    font-weight: 400;
    @apply text-xl
}

/* 自定义滚动条样式 */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #8F7EFF transparent;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 2px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: #8F7EFF;
    border-radius: 2px;
    opacity: 0.6;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #8F7EFF;
    opacity: 1;
}