import { EvmNetwork } from "@/lib/EvmNetwork.ts";

const networks = [
  new EvmNetwork({
    name: "IoTeX",
    fullName: "IoTeX Mainnet",
    chainId: 4689,
    logoUrl: "/images/chain/iotex.svg",
    rpcUrl: "https://babel-api.mainnet.iotex.io/",
    explorerURL: "https://iotexscan.io",
    explorerName: "IoTeX Scan",
    destNetworks: [
      new EvmNetwork({
        name: "Ethereum",
        fullName: "Ethereum Mainnet",
        chainId: 1,
        logoUrl: "/images/chain/eth.svg",
        rpcUrl: "https://ethereum.publicnode.com",
        explorerURL: "https://etherscan.io",
        explorerName: "EtherScan",
      }),
      new EvmNetwork({
        name: "BSC",
        fullName: "BNB Smart Chain Mainnet",
        chainId: 56,
        logoUrl: "/images/chain/bsc.svg",
        rpcUrl:
          "https://rpc.ankr.com/bsc/e6f90ba4600e430626402a1cfd774c2df81ffdb92f1b9e9517e54d8eb2d72c6d",
        explorerURL: "https://bscscan.com",
        explorerName: "BscScan",
      }),
      new EvmNetwork({
        name: "Polygon",
        fullName: "Polygon Mainnet",
        chainId: 137,
        logoUrl: "/images/chain/polygon.svg",
        rpcUrl: "https://polygon-rpc.com",
        explorerURL: "https://polygonscan.com",
        explorerName: "Polygon Explorer",
      }),
      new EvmNetwork({
        name: "Solana",
        fullName: "Solana",
        chainId: 20000,
        logoUrl: "/images/chain/solana.svg",
        rpcUrl:
          "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
        explorerURL: "https://solscan.io",
        explorerName: "SolanaScan",
      }),
    ],
  }),
  new EvmNetwork({
    name: "Ethereum",
    fullName: "Ethereum Mainnet",
    chainId: 1,
    logoUrl: "/images/chain/eth.svg",
    rpcUrl: "https://ethereum.publicnode.com",
    explorerURL: "https://etherscan.io",
    explorerName: "EtherScan",
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
      }),
      new EvmNetwork({
        name: "Solana",
        fullName: "Solana",
        chainId: 20000,
        logoUrl: "/images/chain/solana.svg",
        rpcUrl:
          "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
        explorerURL: "https://solscan.io",
        explorerName: "SolanaScan",
      }),
    ],
  }),
  new EvmNetwork({
    name: "BSC",
    fullName: "BNB Smart Chain Mainnet",
    chainId: 56,
    logoUrl: "/images/chain/bsc.svg",
    rpcUrl:
      "https://rpc.ankr.com/bsc/e6f90ba4600e430626402a1cfd774c2df81ffdb92f1b9e9517e54d8eb2d72c6d",
    explorerURL: "https://bscscan.com",
    explorerName: "BscScan",
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
      }),
    ],
  }),
  new EvmNetwork({
    name: "Polygon",
    fullName: "Polygon Mainnet",
    chainId: 137,
    logoUrl: "/images/chain/polygon.svg",
    rpcUrl: "https://polygon-rpc.com",
    explorerURL: "https://polygonscan.com",
    explorerName: "Polygon Explorer",
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
      }),
    ],
  }),
  new EvmNetwork({
    name: "Solana",
    fullName: "Solana",
    chainId: 20000,
    logoUrl: "/images/chain/solana.svg",
    rpcUrl:
      "https://mainnet.helius-rpc.com/?api-key=f785d5ad-cc19-4566-86f5-9e70a50f676a",
    explorerURL: "https://solscan.io",
    explorerName: "SolanaScan",
    destNetworks: [
      new EvmNetwork({
        name: "IoTeX",
        fullName: "IoTeX Mainnet",
        chainId: 4689,
        logoUrl: "/images/chain/iotex.svg",
        rpcUrl: "https://babel-api.mainnet.iotex.io/",
        explorerURL: "https://iotexscan.io",
        explorerName: "IoTeX Scan",
      }),
      new EvmNetwork({
        name: "Ethereum",
        fullName: "Ethereum Mainnet",
        chainId: 1,
        logoUrl: "/images/chain/eth.svg",
        rpcUrl: "https://ethereum.publicnode.com",
        explorerURL: "https://etherscan.io",
        explorerName: "EtherScan",
      }),
    ],
  }),
];

export { networks };
