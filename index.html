<!doctype html>
<html lang="en">
  <head>
    <meta charset='UTF-8' />
    <link rel='icon' type='image/svg+xml' href='favicon.ico' />
    <meta
      key="viewport"
      content="viewport-fit=cover, width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0"
      name="viewport"
    />
    <meta name="twitter:title" content="ioTube | IoTeX Bridge" />
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:description"
      content="The safe, fast, and secure bridge for exchanging assets between IoTeX and the blockchain universe." />
    <meta name="twitter:site" content="@iotex_io" />
    <meta name="twitter:creator" content="@iotex_io" />
    <meta property="og:site_name" content="ioTube | IoTeX Bridge" />
    <meta property="og:image:width" content="1600" />
    <meta property="og:image:height" content="900" />
    <meta property="og:image" content="https://bridge.iotex.io/images/twitter_share.png" />
    <meta property="og:image:secure_url" content="https://bridge.iotex.io/images/twitter_share.png" />
    <meta property="og:type" content="website" />
    <!-- <link
      href='https://fonts.googleapis.com/css2?family=DM+Mono:ital,wght@0,300;0,400;0,500;1,300;1,400;1,500&display=swap'
      rel='stylesheet'> -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link
      href="https://fonts.googleapis.com/css2?family=DM+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&family=Red+Hat+Display:wght@300;400;500;600;700&display=swap"
      rel="stylesheet">
    <link href='https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap' rel='stylesheet'>
    <title>ioTube | IoTeX Bridge</title>
    <meta name='keywords' content='Decentralized, Multi-asset, Cross Chain Bridge' />
    <meta name='description'
      content='The safe, fast, and secure bridge for exchanging assets between IoTeX and the blockchain universe.'>
<!--    <script type="module">-->
<!--      import { Buffer } from "buffer";-->
<!--      window.Buffer = Buffer;-->
<!--    </script>-->
    <script>
      window.process = {};
      if (global === undefined) {
        var global = window;
      }
    </script>
    <style type='text/css'>
      .seo-hide {
        display: none;
      }
    </style>
  </head>
  <body style="background: #0B0A12">
    <h1 class='seo-hide'>
      <a href='https://bridge.iotex.io/convert' title='ioTube Crosschain Token'>Crosschain Tokens, such as CIOTX, is a wrapped
        token
        that can be bridged to any networks via iotube. You can wrap or unwrap a token at 1:1 ratio anytime. Please switch
        to different networks to see different tokens.</a>
    </h1>
    <h1 class='seo-hide'>
      <a href='https://bridge.iotex.io/transactions' title='ioTube Explorer'>ioTube Explorer</a>
    </h1>
    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
