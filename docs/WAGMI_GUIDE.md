# Wagmi 使用指南

Wagmi 是一个用于以太坊的 React Hooks 库，提供了与区块链交互的强大功能。

## 目录

1. [基础配置](#基础配置)
2. [查询余额](#查询余额)
3. [读取合约](#读取合约)
4. [执行合约](#执行合约)
5. [网络管理](#网络管理)
6. [最佳实践](#最佳实践)

## 基础配置

### 1. 安装依赖

```bash
npm install wagmi viem @tanstack/react-query
```

### 2. 配置 Provider

```tsx
import { WagmiProvider } from 'wagmi';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi';

const queryClient = new QueryClient();
const wagmiAdapter = new WagmiAdapter({
  networks: [mainnet, polygon, bsc],
  projectId: "YOUR_PROJECT_ID",
});

function App() {
  return (
    <WagmiProvider config={wagmiAdapter.wagmiConfig}>
      <QueryClientProvider client={queryClient}>
        <YourApp />
      </QueryClientProvider>
    </WagmiProvider>
  );
}
```

## 查询余额

### 1. 原生代币余额

```tsx
import { useBalance } from 'wagmi';

function BalanceComponent() {
  const { data: balance, isLoading, error } = useBalance({
    address: '0x...',
    query: {
      refetchInterval: 30000, // 30秒自动刷新
      enabled: !!address,     // 仅在有地址时启用
    },
  });

  if (isLoading) return <div>加载中...</div>;
  if (error) return <div>错误: {error.message}</div>;

  return (
    <div>
      余额: {balance?.formatted} {balance?.symbol}
    </div>
  );
}
```

### 2. ERC20 代币余额

```tsx
import { useReadContract } from 'wagmi';
import { formatUnits } from 'viem';

const ERC20_ABI = [
  {
    inputs: [{ name: 'account', type: 'address' }],
    name: 'balanceOf',
    outputs: [{ name: '', type: 'uint256' }],
    stateMutability: 'view',
    type: 'function',
  },
] as const;

function TokenBalance({ tokenAddress, userAddress }) {
  const { data: balance } = useReadContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [userAddress],
  });

  const { data: decimals } = useReadContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'decimals',
  });

  return (
    <div>
      代币余额: {balance && decimals ? formatUnits(balance, decimals) : '0'}
    </div>
  );
}
```

## 读取合约

### 1. 基础读取

```tsx
import { useReadContract } from 'wagmi';

function ContractReader() {
  const { data, isLoading, error } = useReadContract({
    address: '0x...',
    abi: contractABI,
    functionName: 'totalSupply',
    args: [], // 如果函数需要参数
    query: {
      enabled: true,
      refetchInterval: 10000, // 10秒刷新
    },
  });

  return <div>总供应量: {data?.toString()}</div>;
}
```

### 2. 批量读取

```tsx
import { useReadContracts } from 'wagmi';

function BatchReader() {
  const { data, isLoading } = useReadContracts({
    contracts: [
      {
        address: '0x...',
        abi: ERC20_ABI,
        functionName: 'name',
      },
      {
        address: '0x...',
        abi: ERC20_ABI,
        functionName: 'symbol',
      },
      {
        address: '0x...',
        abi: ERC20_ABI,
        functionName: 'totalSupply',
      },
    ],
  });

  if (isLoading) return <div>加载中...</div>;

  const [name, symbol, totalSupply] = data || [];

  return (
    <div>
      <p>名称: {name?.result}</p>
      <p>符号: {symbol?.result}</p>
      <p>总供应量: {totalSupply?.result?.toString()}</p>
    </div>
  );
}
```

## 执行合约

### 1. 基础写入操作

```tsx
import { useWriteContract, useWaitForTransactionReceipt } from 'wagmi';
import { parseUnits } from 'viem';

function TokenTransfer() {
  const { writeContract, data: hash, isPending, error } = useWriteContract();
  
  const { isLoading: isConfirming, isSuccess } = useWaitForTransactionReceipt({
    hash,
  });

  const handleTransfer = async () => {
    writeContract({
      address: '0x...',
      abi: ERC20_ABI,
      functionName: 'transfer',
      args: ['0x...', parseUnits('1.0', 18)],
    });
  };

  return (
    <div>
      <button 
        onClick={handleTransfer}
        disabled={isPending || isConfirming}
      >
        {isPending ? '准备中...' : isConfirming ? '确认中...' : '转账'}
      </button>
      
      {error && <p>错误: {error.message}</p>}
      {isSuccess && <p>转账成功!</p>}
      {hash && <p>交易哈希: {hash}</p>}
    </div>
  );
}
```

### 2. 模拟交易（Gas 估算）

```tsx
import { useSimulateContract } from 'wagmi';

function SimulatedTransfer() {
  const { data: simulateData, error: simulateError } = useSimulateContract({
    address: '0x...',
    abi: ERC20_ABI,
    functionName: 'transfer',
    args: ['0x...', parseUnits('1.0', 18)],
    query: {
      enabled: !!recipient && !!amount,
    },
  });

  const { writeContract } = useWriteContract();

  const handleTransfer = () => {
    if (simulateData?.request) {
      writeContract(simulateData.request);
    }
  };

  return (
    <div>
      {simulateError && <p>模拟失败: {simulateError.message}</p>}
      <button 
        onClick={handleTransfer}
        disabled={!simulateData || !!simulateError}
      >
        转账
      </button>
    </div>
  );
}
```

### 3. 批量操作

```tsx
function BatchOperations() {
  const { writeContract } = useWriteContract();

  const handleBatchApprove = async () => {
    const approvals = [
      { spender: '0x...', amount: parseUnits('100', 18) },
      { spender: '0x...', amount: parseUnits('200', 18) },
    ];

    for (const approval of approvals) {
      await writeContract({
        address: tokenAddress,
        abi: ERC20_ABI,
        functionName: 'approve',
        args: [approval.spender, approval.amount],
      });
    }
  };

  return (
    <button onClick={handleBatchApprove}>
      批量授权
    </button>
  );
}
```

## 网络管理

### 1. 获取当前网络

```tsx
import { useChainId, useAccount } from 'wagmi';

function NetworkInfo() {
  const chainId = useChainId();
  const { chain } = useAccount();

  return (
    <div>
      <p>网络 ID: {chainId}</p>
      <p>网络名称: {chain?.name}</p>
      <p>原生代币: {chain?.nativeCurrency?.symbol}</p>
    </div>
  );
}
```

### 2. 切换网络

```tsx
import { useSwitchChain } from 'wagmi';

function NetworkSwitcher() {
  const { switchChain, isPending } = useSwitchChain();

  const networks = [
    { id: 1, name: 'Ethereum' },
    { id: 56, name: 'BSC' },
    { id: 137, name: 'Polygon' },
    { id: 4689, name: 'IoTeX' },
  ];

  return (
    <div>
      {networks.map((network) => (
        <button
          key={network.id}
          onClick={() => switchChain({ chainId: network.id })}
          disabled={isPending}
        >
          {network.name}
        </button>
      ))}
    </div>
  );
}
```

## 最佳实践

### 1. 错误处理

```tsx
function RobustComponent() {
  const { data, isLoading, error, refetch } = useBalance({
    address: userAddress,
    query: {
      retry: 3,
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  });

  if (error) {
    return (
      <div>
        <p>加载失败: {error.message}</p>
        <button onClick={() => refetch()}>重试</button>
      </div>
    );
  }

  return <div>{/* 正常内容 */}</div>;
}
```

### 2. 性能优化

```tsx
import { useMemo } from 'react';

function OptimizedComponent() {
  const { data: balance } = useBalance({ address });
  
  // 缓存计算结果
  const formattedBalance = useMemo(() => {
    if (!balance) return '0';
    return formatUnits(balance.value, balance.decimals);
  }, [balance]);

  // 使用 enabled 控制查询时机
  const { data: tokenData } = useReadContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [userAddress],
    query: {
      enabled: !!userAddress && !!tokenAddress,
      staleTime: 30000, // 30秒内认为数据新鲜
    },
  });

  return <div>{formattedBalance}</div>;
}
```

### 3. 类型安全

```tsx
import { Address } from 'viem';

interface TokenInfo {
  address: Address;
  symbol: string;
  decimals: number;
}

function TypeSafeComponent({ token }: { token: TokenInfo }) {
  const { data: balance } = useReadContract({
    address: token.address,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [userAddress as Address],
  }) as { data: bigint | undefined };

  return (
    <div>
      {balance ? formatUnits(balance, token.decimals) : '0'} {token.symbol}
    </div>
  );
}
```

### 4. 自定义 Hook

```tsx
function useTokenBalance(tokenAddress: Address, userAddress?: Address) {
  const { data: balance } = useReadContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'balanceOf',
    args: [userAddress!],
    query: {
      enabled: !!userAddress,
    },
  });

  const { data: decimals } = useReadContract({
    address: tokenAddress,
    abi: ERC20_ABI,
    functionName: 'decimals',
  });

  const formattedBalance = useMemo(() => {
    if (!balance || !decimals) return '0';
    return formatUnits(balance as bigint, decimals);
  }, [balance, decimals]);

  return {
    balance: balance as bigint | undefined,
    decimals,
    formattedBalance,
  };
}
```

## 常见问题

### 1. 交易失败处理

```tsx
function TransactionHandler() {
  const { writeContract, error } = useWriteContract();

  const handleTransaction = () => {
    try {
      writeContract({
        // 交易参数
      });
    } catch (err) {
      if (err.message.includes('User rejected')) {
        console.log('用户取消交易');
      } else if (err.message.includes('insufficient funds')) {
        console.log('余额不足');
      } else {
        console.error('交易失败:', err);
      }
    }
  };
}
```

### 2. 网络不匹配处理

```tsx
function NetworkGuard({ children, requiredChainId }) {
  const chainId = useChainId();
  const { switchChain } = useSwitchChain();

  if (chainId !== requiredChainId) {
    return (
      <div>
        <p>请切换到正确的网络</p>
        <button onClick={() => switchChain({ chainId: requiredChainId })}>
          切换网络
        </button>
      </div>
    );
  }

  return children;
}
```
